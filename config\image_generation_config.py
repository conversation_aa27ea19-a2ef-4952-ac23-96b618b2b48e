# config/image_generation_config.py
import os
from typing import Dict, Any

PROVIDER_CLASSES: Dict[str, str] = {
    "pollinations": "app.services.image_generation.providers.pollinations.PollinationsGenerator",
    "openai": "app.services.image_generation.providers.openai.OpenaiGenerator",
}

IMAGE_MODELS_CONFIG: Dict[str, Dict[str, Any]] = {
    "flux": {
        "name": "Flux",
        "provider": "pollinations",
        "params": {
            "base_url": "https://image.pollinations.ai/prompt",
            "model_name_api": "flux",
            "timeout": 45,
            "max_retries": 3,
            "retry_delay": 10,
            "api_token_env": "POLLINATIONS_API_TOKEN",
            "default_params": {  # <-- ПЕРЕМЕЩЕНО СЮДА
                "nologo": "true",
                "private": "true",
                "safe": "false",
            }
        },
    },

    "kontext": {
        "name": "kontext",
        "provider": "pollinations",
        "params": {
            "base_url": "https://image.pollinations.ai/prompt",
            "model_name_api": "kontext",
            "timeout": 45,
            "max_retries": 3,
            "retry_delay": 10,
            "api_token_env": "POLLINATIONS_API_TOKEN",
            "default_params": {
                "nologo": "true",
                "private": "true",
                "safe": "false",
            }
        },
    },
    "nanobanana": {
        "name": "Gemini 2.5 Flash Image",
        "provider": "pollinations",
        "params": {
            "base_url": "https://image.pollinations.ai/prompt",
            "model_name_api": "nanobanana",
            "timeout": 45,
            "max_retries": 3,
            "retry_delay": 10,
            "api_token_env": "POLLINATIONS_API_TOKEN",
            "default_params": {
                "nologo": "true",
                "private": "true",
                "safe": "false",
            }
        },
    },
    "provider-4/imagen-4": {
        "name": "Imagen-4",
        "provider": "openai",
        "params": {
           "base_url": "https://api.a4f.co/v1",
            "model": "provider-4/imagen-4",
            "timeout": 120,
            "max_retries": 3,
            "retry_delay": 10,
            "api_key_env": "OPENAI_IMAGE_KEY",
            "max_prompt_length": 100,
        },
    },
}

DEFAULT_IMAGE_MODEL = "flux"

IMAGE_GENERATION_SETTINGS = {
    "max_prompt_length": 250,
    "available_sizes": {
        "1:1": (1024, 1024),
        "16:9": (1920, 1080),
        "9:16": (1080, 1920),
    },
    "default_size": "1:1",
    # Ключ "default_params" отсюда удален
}

def get_image_dimensions(size_key: str) -> tuple[int, int]:
    return IMAGE_GENERATION_SETTINGS["available_sizes"].get(
        size_key, IMAGE_GENERATION_SETTINGS["available_sizes"][IMAGE_GENERATION_SETTINGS["default_size"]]
    )
