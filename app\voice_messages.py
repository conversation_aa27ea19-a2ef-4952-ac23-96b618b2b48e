import os
import speech_recognition as sr
import asyncio
import aiofiles
from typing import List
from pydub import AudioSegment
from aiogram.types import Message, BufferedInputFile
from aiogram.enums import ParseMode
from aiogram import Dispatcher
import tempfile
import logging

from app.text_messages import llm_response
from app.supabase.messages import save_message
from app.supabase.users import get_user_by_tg_id, update_user_limits, get_user_language
from app.subscription_manager import SubscriptionManager
from app.localization import get_text
from app.utils.request_limiter import check_limits
from app.utils.formatting import convert_markdown_to_html
from app.services.tts.manager import get_generator as get_tts_generator

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Загрузка переменных окружения
from dotenv import load_dotenv
load_dotenv()

MAX_VOICE_SIZE = 10 * 1024 * 1024  # 10 МБ

async def download_voice(message: Message, file_path: str) -> str:
    file_info = await message.bot.get_file(message.voice.file_id)
    await message.bot.download_file(file_info.file_path, file_path)
    return file_path

async def convert_ogg_to_wav(ogg_path: str, wav_path: str):
    def sync_convert():
        try:
            if not os.path.exists(ogg_path):
                raise FileNotFoundError(f"Исходный файл не найден: {ogg_path}")

            if os.path.getsize(ogg_path) == 0:
                raise ValueError("Исходный файл пуст")

            audio = AudioSegment.from_ogg(ogg_path)
            audio.export(wav_path, format="wav")

            if not os.path.exists(wav_path):
                raise RuntimeError("WAV файл не был создан")
        except FileNotFoundError as e:
            logger.error(f"Ошибка (sync): {e}")
            raise
        except ValueError as e:
            logger.error(f"Ошибка (sync): {e}")
            raise
        except Exception as e:
            logger.error(f"Неожиданная ошибка при конвертации (sync): {e}")
            raise

    try:
        await asyncio.to_thread(sync_convert)
    except Exception as e:
        logger.error(f"Ошибка выполнения convert_ogg_to_wav в потоке: {e}")
        raise

async def speech_to_text(wav_path: str) -> str:
    def sync_recognize():
        recognizer = sr.Recognizer()
        try:
            with sr.AudioFile(wav_path) as source:
                audio_data = recognizer.record(source)
            # Важно: recognize_google - это сетевая операция, она тоже блокирующая
            text = recognizer.recognize_google(audio_data, language="ru-RU")
            return text
        except sr.UnknownValueError:
            logger.error("Google Speech Recognition не смогла распознать аудио")
            raise
        except sr.RequestError as e:
            logger.error(f"Не удалось запросить результаты у Google Speech Recognition; {e}")
            raise
        except Exception as e:
            logger.error(f"Неожиданная ошибка при распознавании (sync): {e}")
            raise

    try:
        text = await asyncio.to_thread(sync_recognize)
        return text
    except Exception as e:
        logger.error(f"Ошибка выполнения speech_to_text в потоке: {e}")
        raise

async def handle_voice_message(message: Message, user_id: int, dispatcher: Dispatcher):
    # Проверяем лимиты и получаем пользователя
    can_proceed, user = await check_limits(message, "text")
    if not can_proceed:
        return

    processing_message = None
    try:
        if message.voice.file_size > MAX_VOICE_SIZE:
            await message.answer("Голосовое сообщение слишком большое. Максимальный размер: 10 МБ.")
            return

        # Отправляем сообщение о начале обработки из TEXTS
        user_language = await get_user_language(user_id)
        voice_processing_message = get_text("errors", "voice_processing", user_language)
        processing_message = await message.answer(voice_processing_message)

        with tempfile.TemporaryDirectory() as temp_dir:
            ogg_file_path = os.path.join(temp_dir, f"voice_{message.message_id}.ogg")
            wav_file_path = os.path.join(temp_dir, f"voice_{message.message_id}.wav")

            # Скачивание голосового сообщения
            try:
                await download_voice(message, ogg_file_path)
            except Exception as e:
                logger.error(f"Ошибка при скачивании голосового сообщения: {e}")
                await message.answer("Ошибка при получении голосового сообщения. Попробуйте еще раз.")
                if processing_message:
                    await processing_message.delete()
                return

            # Конвертация OGG в WAV
            try:
                await convert_ogg_to_wav(ogg_file_path, wav_file_path)
            except FileNotFoundError:
                await message.answer("Ошибка: файл голосового сообщения не найден. Попробуйте отправить сообщение еще раз.")
                if processing_message:
                    await processing_message.delete()
                return
            except ValueError:
                await message.answer("Ошибка: полученный файл голосового сообщения пуст. Попробуйте записать новое сообщение.")
                if processing_message:
                    await processing_message.delete()
                return
            except Exception as e:
                logger.error(f"Ошибка при конвертации аудио: {e}")
                await message.answer(f"Ошибка при обработке аудио. Попробуйте еще раз или обратитесь в поддержку.")
                if processing_message:
                    await processing_message.delete()
                return

            # Преобразование речи в текст
            try:
                text = await speech_to_text(wav_file_path)
                formatted_message = f'голосовое сообщение: [{text}]'
            except Exception as e:
                logger.error(f"Ошибка при распознавании речи: {e}")
                await message.answer("Не удалось распознать речь. Попробуйте еще раз.")
                if processing_message:
                    await processing_message.delete()
                return

            # Сохранение сообщения в базу данных Supabase
            await save_message(role="user", content=formatted_message, user_id=user["id"])

            # Получение ответа от llm_response
            try:
                mcp_manager_instance = dispatcher.get('mcp_manager')

                async def notify_tool_usage(tool_names: List[str]):
                    user_language = await get_user_language(user_id)
                    tool_usage_message = get_text("text_processing", "using_mcp_tools", user_language)
                    if processing_message: await processing_message.edit_text(tool_usage_message)

                response = await llm_response(
                    text,
                    user_id,
                    mcp_manager_instance,
                    bot=message.bot,
                    message=message,
                    tool_usage_callback=notify_tool_usage
                )
                if "error" in response:
                    if processing_message:
                        await processing_message.edit_text(response["error"])
                    return
                response_text = response.choices[0].message.content
                html_response_text = convert_markdown_to_html(response_text)
            except Exception as e:
                logger.error(f"Ошибка при генерации ответа llm_response: {e}")
                await message.answer("Ошибка при генерации ответа. Попробуйте еще раз.")
                if processing_message:
                    await processing_message.delete()
                return

            # Сохранение ответа в базу данных Supabase
            await save_message(role="assistant", content=response_text, user_id=user["id"])

            # --- НОВАЯ ЛОГИКА СИНТЕЗА РЕЧИ ---
            await message.bot.send_chat_action(chat_id=message.chat.id, action="record_voice")
            
            tts_generator = get_tts_generator("default_v1")
            audio_bytes = await tts_generator.generate(response_text, user["id"])

            if audio_bytes:
                try:
                    if processing_message:
                        await processing_message.delete()
                        processing_message = None

                    await message.bot.send_chat_action(chat_id=message.chat.id, action="upload_voice")
                    voice_response = BufferedInputFile(audio_bytes, filename="response.mp3")
                    await message.answer_voice(voice_response)
                except Exception as e:
                    logger.error(f"Ошибка при отправке голосового ответа: {e}")
                    # Fallback на текстовый ответ, если отправка голоса не удалась
                    if processing_message:
                        await processing_message.edit_text(html_response_text, parse_mode=ParseMode.HTML)
            else:
                # Fallback на текстовый ответ, если синтез речи не удался
                logger.warning("Синтез речи не удался, отправляется текстовый ответ.")
                if processing_message:
                    try:
                        await processing_message.edit_text(html_response_text, parse_mode=ParseMode.HTML)
                    except Exception as e:
                        logger.error(f"Ошибка отправки HTML сообщения (Voice Fallback): {e}")
                        await processing_message.edit_text("Не удалось отформатировать ответ. Отправляю как есть:\n\n" + response_text, parse_mode=None)
            # --- КОНЕЦ НОВОЙ ЛОГИКИ ---

        # Обновляем счетчик запросов в Supabase
        current_moscow_time = SubscriptionManager.get_moscow_time()
        user_data = await get_user_by_tg_id(user_id)
        if user_data:
            text_requests = user_data["text_requests_today"] + 1
            image_requests = user_data["image_requests_today"]
            await update_user_limits(
                user_data["id"],
                text_requests,
                image_requests,
                current_moscow_time.isoformat()
            )

    except Exception as e:
        logger.error(f"Неожиданная ошибка в handle_voice_message: {e}", exc_info=True)
        try:
            await message.answer("Произошла неожиданная ошибка. Пожалуйста, попробуйте еще раз позже.")
        except Exception as inner_e:
            logger.error(f"Ошибка при отправке сообщения об ошибке пользователю: {inner_e}")

        if processing_message:
            try:
                await processing_message.delete()
            except Exception as del_e:
                logger.error(f"Ошибка при удалении processing_message в блоке except: {del_e}")
