# app/tools/implementations/edit_image_tool.py

import logging
import asyncio
import aiohttp
import urllib.parse
import os
from typing import Optional
from aiogram import Bo<PERSON>
from aiogram.types import BufferedInputFile

from app.tools.base_tool import Tool
from app.supabase.users import get_user_by_tg_id, set_active_image_file_id, get_user_language
from app.services.file_hosting.uguu_uploader import UguuUploader
from config.image_generation_config import IMAGE_MODELS_CONFIG
from app.localization import get_text

logger = logging.getLogger(__name__)

class EditImageTool:
    """
    Инструмент для редактирования изображения на основе последнего контекста.
    """
    
    name: str = "edit_image"
    description: str = "Edits the last sent or generated image based on a user's text description. Use this when the user asks to change, modify, or edit an existing picture. Do not use it for generating new images from scratch."
    schema: dict = {
        "type": "object",
        "properties": {
            "prompt": {
                "type": "string",
                "description": "A detailed text description of the changes to be made to the image. IMPORTANT: Always write the description in English, regardless of the user's language."
            }
        },
        "required": ["prompt"]
    }
    
    def __init__(self):
        self.uploader = UguuUploader()
        self.edit_model_name = "nanobanana"
        self.model_config = IMAGE_MODELS_CONFIG.get(self.edit_model_name, {})
        if not self.model_config:
            logger.error(f"Конфигурация для модели '{self.edit_model_name}' не найдена!")
            
    async def execute(self, bot: Bot, chat_id: int, prompt: str) -> str:
        user_id = chat_id
        user_language = await get_user_language(user_id)
        
        try:
            user = await get_user_by_tg_id(user_id)
            if not user or not user.get('active_image_file_id'):
                logger.warning(f"Не найден контекст изображения для редактирования у пользователя {user_id}")
                return get_text("edit_image", "no_context", user_language)
            
            file_id = user['active_image_file_id']

            await bot.send_message(chat_id, get_text("edit_image", "processing", user_language))

            image_url = await self.uploader.upload_by_file_id(bot, file_id)
            if not image_url:
                logger.error(f"Не удалось получить URL для file_id {file_id}")
                return get_text("edit_image", "upload_error", user_language)

            edited_image_bytes = await self.call_pollinations_api(prompt, image_url)
            if not edited_image_bytes:
                logger.error(f"API редактирования не вернуло изображение для промпта: {prompt}")
                return get_text("edit_image", "api_error", user_language)

            photo = BufferedInputFile(edited_image_bytes, filename="edited_image.png")
            
            # --- ИЗМЕНЕННАЯ ЛОГИКА ПОДПИСИ ---
            # Получаем шаблон из общего файла локализации, как для генерации
            caption_template = get_text("common", "tools", user_language)["model_caption"]
            # Получаем отображаемое имя модели из конфига
            model_display_name = self.model_config.get("name", self.edit_model_name)
            caption = caption_template.format(model_name=model_display_name)
            # ---------------------------------

            sent_message = await bot.send_photo(chat_id=chat_id, photo=photo, caption=caption)

            if sent_message and sent_message.photo:
                new_file_id = sent_message.photo[-1].file_id
                await set_active_image_file_id(user_id, new_file_id)
                logger.info(f"Контекст изображения обновлен на новый file_id {new_file_id} для пользователя {user_id}")

            return get_text("edit_image", "success", user_language)

        except Exception as e:
            logger.error(f"Ошибка выполнения инструмента edit_image: {e}", exc_info=True)
            return get_text("edit_image", "general_error", user_language)

    async def call_pollinations_api(self, prompt: str, image_url: str) -> Optional[bytes]:
        if not self.model_config: return None

        params_config = self.model_config.get("params", {})
        base_url = params_config.get("base_url")
        timeout_s = params_config.get("timeout", 45)
        max_retries = params_config.get("max_retries", 3)
        retry_delay = params_config.get("retry_delay", 10)
        api_token_env = params_config.get("api_token_env")
        default_params = params_config.get("default_params", {})
        
        encoded_prompt = urllib.parse.quote(prompt)
        
        params = {
            "model": self.edit_model_name,
            "image": image_url,
            **default_params
        }

        if api_token_env and os.getenv(api_token_env):
             params["token"] = os.getenv(api_token_env)

        query_string = urllib.parse.urlencode(params)
        full_url = f"{base_url}/{encoded_prompt}?{query_string}"
        
        logger.info(f"Вызов API редактирования: {full_url}")

        headers = {}
        token_val = os.getenv(api_token_env) if api_token_env else None
        if token_val:
            headers["Authorization"] = f"Bearer {token_val}"

        attempt = 0
        while attempt < max_retries:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(full_url, headers=headers, timeout=timeout_s) as response:
                        if response.status == 200:
                            return await response.read()
                        # Для 5xx сделаем повтор
                        if 500 <= response.status < 600:
                            attempt += 1
                            logger.warning(f"Pollinations вернул {response.status}. Повтор через {retry_delay}с (попытка {attempt}/{max_retries})")
                            if attempt < max_retries:
                                await asyncio.sleep(retry_delay)
                                continue
                        # Для остальных статусов — лог и выход
                        response.raise_for_status()
                        return None
            except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                attempt += 1
                logger.warning(f"Сетевая ошибка Pollinations: {e}. Повтор через {retry_delay}с (попытка {attempt}/{max_retries})")
                if attempt < max_retries:
                    await asyncio.sleep(retry_delay)
                    continue
                return None
            except Exception as e:
                logger.error(f"Ошибка при вызове API Pollinations: {e}")
                return None
        return None
