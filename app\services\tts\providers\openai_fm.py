# app/services/tts/providers/openai_fm.py
import os
import logging
import asyncio
import aiohttp
import tempfile
import aiofiles
from typing import Any, Dict, Optional, List
from pydub import AudioSegment

from app.services.tts.base import TextToSpeechGenerator
from config.roles_config import ROLES_CONFIG, DEFAULT_ROLE_ID
from app.supabase.users import get_user_role

logger = logging.getLogger(__name__)

MAX_CONCURRENT_TTS_REQUESTS = 3
tts_semaphore = asyncio.Semaphore(MAX_CONCURRENT_TTS_REQUESTS)

class OpenaiFmGenerator(TextToSpeechGenerator):
    """
    Провайдер для синтеза речи через кастомный API, совместимый с openai.fm.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.base_url = self.config.get("base_url")
        self.timeout = self.config.get("timeout", 90)
        self.max_retries = self.config.get("max_retries", 2)
        self.retry_delay = self.config.get("retry_delay", 5)
        self.chunk_limit = self.config.get("chunk_limit", 990)
        
        if not self.base_url:
            raise ValueError("В конфигурации для OpenaiFmGenerator отсутствует 'base_url'")
        
        logger.info(f"Инициализирован OpenaiFmGenerator с base_url: {self.base_url}")

    async def generate(self, text: str, user_id: int) -> Optional[bytes]:
        """
        Асинхронная функция для преобразования текста в речь.
        Поддерживает длинные тексты путем разбиения на чанки и последующего объединения аудио.
        """
        user_role = await get_user_role(user_id)
        role_config = ROLES_CONFIG.get(user_role, ROLES_CONFIG[DEFAULT_ROLE_ID])
        voice = role_config.get("voice", "alloy")
        vibe_prompt = os.getenv("TTS_VIBE_PROMPT", "")

        logger.info(f"Синтез речи для user_id={user_id}. Роль: {user_role}, Голос: {voice}.")
        if vibe_prompt:
            logger.info(f"Используется TTS_VIBE_PROMPT.")

        chunks = self._split_text_into_chunks(text)
        logger.info(f"Текст разбит на {len(chunks)} чанков для синтеза.")

        temp_dir = tempfile.mkdtemp()
        chunk_files = []
        output_file = os.path.join(temp_dir, "final_response.mp3")

        try:
            tasks = []
            for i, chunk in enumerate(chunks):
                chunk_file = os.path.join(temp_dir, f"chunk_{i}.mp3")
                chunk_files.append(chunk_file)
                tasks.append(self._synthesize_chunk(chunk, chunk_file, voice, vibe_prompt))

            results = await asyncio.gather(*tasks)
            if not all(results):
                logger.error("Не удалось синтезировать один или несколько чанков.")
                return None

            merge_success = await self._merge_audio_files(chunk_files, output_file)
            if not merge_success:
                logger.error("Не удалось объединить аудио-чанки.")
                return None

            async with aiofiles.open(output_file, 'rb') as f:
                audio_bytes = await f.read()
            
            logger.info(f"Синтез речи успешно завершен. Размер аудио: {len(audio_bytes)} байт.")
            return audio_bytes

        except Exception as e:
            logger.error(f"Ошибка при генерации TTS: {e}", exc_info=True)
            return None
        finally:
            # Гарантированная очистка временных файлов
            for file_path in chunk_files + [output_file]:
                if os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                    except Exception as e:
                        logger.error(f"Ошибка при удалении временного файла {file_path}: {e}")
            if os.path.exists(temp_dir):
                try:
                    os.rmdir(temp_dir)
                except Exception as e:
                    logger.error(f"Ошибка при удалении временной директории {temp_dir}: {e}")

    async def _synthesize_chunk(self, text: str, output_file: str, voice: str, vibe_prompt: Optional[str]) -> bool:
        params = {'input': text, 'voice': voice}
        if vibe_prompt:
            params['prompt'] = vibe_prompt

        async with tts_semaphore:
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                    logger.info(f"Отправка запроса к TTS API для чанка длиной {len(text)} символов")
                    async with session.get(self.base_url, params=params) as response:
                        if response.status == 200:
                            content = await response.read()
                            async with aiofiles.open(output_file, 'wb') as f:
                                await f.write(content)
                            logger.info(f"TTS API успешно вернул чанк.")
                            return True
                        else:
                            error_text = await response.text()
                            logger.error(f"Ошибка API TTS: статус {response.status}, ответ: {error_text}")
                            return False
            except Exception as e:
                logger.error(f"Ошибка при обращении к API TTS для чанка: {e}")
                return False

    def _split_text_into_chunks(self, text: str) -> List[str]:
        if len(text) <= self.chunk_limit:
            return [text]
        
        chunks = []
        current_position = 0
        while current_position < len(text):
            chunk_end = min(current_position + self.chunk_limit, len(text))
            if chunk_end < len(text):
                # Ищем лучшие точки для разрыва
                sentence_end = max(text.rfind(p, current_position, chunk_end) for p in (". ", "! ", "? ", ".\n", "!\n", "?\n"))
                paragraph_end = text.rfind("\n\n", current_position, chunk_end)
                
                best_split = max(sentence_end, paragraph_end)
                if best_split != -1:
                    # +1 или +2 для захвата знака препинания/переноса строки
                    chunk_end = best_split + (2 if text[best_split:best_split+2] == "\n\n" else 1)
                else:
                    space = text.rfind(" ", current_position, chunk_end)
                    if space != -1:
                        chunk_end = space + 1
            
            chunks.append(text[current_position:chunk_end].strip())
            current_position = chunk_end
        
        return [c for c in chunks if c] # Убираем пустые чанки

    async def _merge_audio_files(self, chunk_files: List[str], output_file: str) -> bool:
        def sync_merge():
            try:
                if not chunk_files:
                    logger.error("Нет аудио-чанков для объединения.")
                    return False
                
                combined = AudioSegment.empty()
                for file_path in chunk_files:
                    if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
                        segment = AudioSegment.from_mp3(file_path)
                        combined += segment
                    else:
                        logger.warning(f"Пропуск файла для объединения: {file_path} (не существует или пуст)")
                
                if len(combined) == 0:
                    logger.error("Не удалось создать объединенный аудиофайл, результат пуст.")
                    return False

                combined.export(output_file, format="mp3")
                return True
            except Exception as e:
                logger.error(f"Ошибка при объединении аудиофайлов (sync): {e}", exc_info=True)
                return False

        return await asyncio.to_thread(sync_merge)
