-- =================================================================
-- ЕДИНЫЙ СКРИПТ ИНИЦИАЛИЗАЦИИ БАЗЫ ДАННЫХ SUPABASE
-- =================================================================
-- Этот скрипт объединяет все необходимые миграции в один файл для
-- чистой и корректной установки базы данных с нуля.
--
-- ПОРЯДОК ВЫПОЛНЕНИЯ:
-- 1. Создание таблиц, индексов и политик RLS.
-- 2. Создание БЕЗОПАСНОЙ функции и триггера для updated_at.
-- 3. Добавление дополнительных полей в таблицу users.
-- 4. Добавление комментариев.
--
-- Инструкция:
-- 1. Войдите в панель управления Supabase.
-- 2. Перейдите в "SQL Editor".
-- 3. Вставьте содержимое этого файла и выполните его.
-- =================================================================

-- Установка часового пояса для сессии и базы данных
ALTER DATABASE postgres SET timezone TO 'Europe/Moscow';
SET timezone TO 'Europe/Moscow';

-- =============================================
-- ЧАСТЬ 1: СОЗДАНИЕ ТАБЛИЦ И ИНДЕКСОВ
-- =============================================

-- Создание таблицы пользователей
CREATE TABLE public.users (
    id BIGSERIAL PRIMARY KEY,
    tg_id BIGINT NOT NULL UNIQUE,
    model VARCHAR NOT NULL,
    role VARCHAR NOT NULL,
    referral_code VARCHAR NOT NULL UNIQUE,
    referrer_id BIGINT REFERENCES public.users(id),
    referral_count INTEGER NOT NULL DEFAULT 0,
    subscription_plan VARCHAR NOT NULL,
    subscription_end_date TIMESTAMP WITH TIME ZONE,
    text_requests_today INTEGER NOT NULL DEFAULT 0,
    image_requests_today INTEGER NOT NULL DEFAULT 0,
    last_request_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT timezone('Europe/Moscow', NOW()),
    language VARCHAR(10) NOT NULL DEFAULT 'ru',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT timezone('Europe/Moscow', NOW()),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT timezone('Europe/Moscow', NOW())
);

-- Создание таблицы сообщений
CREATE TABLE public.messages (
    id BIGSERIAL PRIMARY KEY,
    role VARCHAR NOT NULL,
    content TEXT NOT NULL,
    user_id BIGINT NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    image_data TEXT,
    caption TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT timezone('Europe/Moscow', NOW())
);

-- Создание индексов для оптимизации запросов
CREATE INDEX idx_users_tg_id ON public.users(tg_id);
CREATE INDEX idx_users_referral_code ON public.users(referral_code);
CREATE INDEX idx_messages_user_id ON public.messages(user_id);

-- =============================================
-- ЧАСТЬ 2: СОЗДАНИЕ БЕЗОПАСНОГО ТРИГГЕРА
-- =============================================

-- Создание безопасной функции с правильными настройками безопасности
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    -- Обновляем поле updated_at текущим временем в московском часовом поясе
    NEW.updated_at = timezone('Europe/Moscow', NOW());
    RETURN NEW;
END;
$$;

-- Устанавливаем владельца функции
ALTER FUNCTION public.update_updated_at_column() OWNER TO postgres;

-- Создаем триггер с использованием безопасной функции
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- =============================================
-- ЧАСТЬ 3: ДОБАВЛЕНИЕ ДОПОЛНИТЕЛЬНЫХ ПОЛЕЙ
-- =============================================

-- Добавляем поле image_model в таблицу users
ALTER TABLE public.users
ADD COLUMN image_model VARCHAR NOT NULL DEFAULT 'flux';

-- Добавляем поле active_image_file_id в таблицу users
ALTER TABLE public.users
ADD COLUMN active_image_file_id TEXT;

-- =============================================
-- ЧАСТЬ 4: НАСТРОЙКА ROW LEVEL SECURITY (RLS)
-- =============================================

-- Включение RLS для таблиц
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;

-- Политики для таблицы users (разрешаем анонимному ключу все операции для простоты разработки)
CREATE POLICY "Allow anon access to users" ON public.users
    FOR ALL
    TO anon
    USING (true)
    WITH CHECK (true);

-- Политики для таблицы messages (разрешаем анонимному ключу все операции)
CREATE POLICY "Allow anon access to messages" ON public.messages
    FOR ALL
    TO anon
    USING (true)
    WITH CHECK (true);

-- =============================================
-- ЧАСТЬ 5: ДОБАВЛЕНИЕ КОММЕНТАРИЕВ
-- =============================================

-- Комментарии к таблице users
COMMENT ON TABLE public.users IS 'Пользователи Telegram-бота';
COMMENT ON COLUMN public.users.tg_id IS 'ID пользователя в Telegram';
COMMENT ON COLUMN public.users.model IS 'Модель AI, используемая пользователем';
COMMENT ON COLUMN public.users.role IS 'Роль пользователя в системе';
COMMENT ON COLUMN public.users.referral_code IS 'Реферальный код пользователя';
COMMENT ON COLUMN public.users.referrer_id IS 'ID пользователя, пригласившего текущего пользователя';
COMMENT ON COLUMN public.users.subscription_plan IS 'План подписки пользователя';
COMMENT ON COLUMN public.users.text_requests_today IS 'Количество текстовых запросов за сегодня';
COMMENT ON COLUMN public.users.image_requests_today IS 'Количество запросов на генерацию изображений за сегодня';
COMMENT ON COLUMN public.users.last_request_date IS 'Дата и время последнего запроса';
COMMENT ON COLUMN public.users.language IS 'Язык интерфейса пользователя (ru, en)';
COMMENT ON COLUMN public.users.created_at IS 'Дата и время создания записи';
COMMENT ON COLUMN public.users.updated_at IS 'Дата и время последнего обновления записи';
COMMENT ON COLUMN public.users.image_model IS 'Модель для генерации изображений, используемая пользователем (flux, gptimage)';
COMMENT ON COLUMN public.users.active_image_file_id IS 'Хранит file_id последнего изображения для контекста редактирования.';

-- Комментарии к таблице messages
COMMENT ON TABLE public.messages IS 'Сообщения пользователей и ответы бота';
COMMENT ON COLUMN public.messages.role IS 'Роль отправителя сообщения (user/assistant)';
COMMENT ON COLUMN public.messages.content IS 'Текстовое содержимое сообщения';
COMMENT ON COLUMN public.messages.user_id IS 'ID пользователя, связанного с сообщением';
COMMENT ON COLUMN public.messages.image_data IS 'Данные изображения (если есть)';
COMMENT ON COLUMN public.messages.caption IS 'Подпись к изображению (если есть)';
COMMENT ON COLUMN public.messages.created_at IS 'Дата и время создания сообщения';

-- Комментарий к функции
COMMENT ON FUNCTION public.update_updated_at_column() IS
'Безопасная функция для автоматического обновления поля updated_at при изменении записи.';

-- Комментарий к триггеру
COMMENT ON TRIGGER update_users_updated_at ON public.users IS
'Триггер для автоматического обновления поля updated_at при изменении записи пользователя.';

-- =============================================
-- ЧАСТЬ 6: МИГРАЦИЯ ДАННЫХ (ОПЦИОНАЛЬНО)
-- =============================================
-- Этот блок оставлен для совместимости, если скрипт будет применен
-- к базе со старыми данными. Для новой установки он ничего не сделает.
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='users' AND column_name='role') THEN
        UPDATE public.users
        SET role =
            CASE
                WHEN role = 'Default.txt' THEN 'default'
                WHEN role = 'Emily.txt' THEN 'emily'
                WHEN role = 'Lsaac.txt' THEN 'isaac'
                WHEN role = 'Tutor.txt' THEN 'tutor'
                ELSE role
            END
        WHERE role LIKE '%.txt';
    END IF;
END $$;


-- =============================================
-- ЗАВЕРШЕНИЕ
-- =============================================
DO $$
BEGIN
    RAISE NOTICE 'Скрипт инициализации базы данных успешно выполнен.';
    RAISE NOTICE 'Все таблицы, триггеры и политики безопасности созданы.';
END $$;
