# config/tts_config.py

# Пути к классам провайдеров
TTS_PROVIDER_CLASSES = {
    "openai_fm": "app.services.tts.providers.openai_fm.OpenaiFmGenerator",
}

# Конфигурация моделей TTS
TTS_MODELS_CONFIG = {
    "default_v1": {
        "provider": "openai_fm",
        "params": {
            "base_url": "https://www.openai.fm/api/generate",
            "timeout": 90, # Увеличим таймаут для длинных запросов
            "max_retries": 2,
            "retry_delay": 5,
            "chunk_limit": 990, # Макс. символов на чанк
        }
    },
}
