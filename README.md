## Telegram AI Chatbot (приватный)

Многофункциональный Telegram-бот с ИИ: диалоги на выбранных моделях, анализ изображений, генерация картинок, голосовые сообщения и интеграция инструментов. Проект приватный — репозиторий и инструкции предназначены только для владельца.

### 🌟 Возможности

- **Языковые модели**:
  - GPT-4.1, GPT-4.1‑nano, GPT‑5‑nano
  - Gemini 2.5 Pro
  - Вызов инструментов (Tool Calling), поддержка Vision
- **Генерация изображений**:
  - Модели: Flux, kontext (Pollinations), Imagen‑4 (OpenAI‑совместимый провайдер)
  - Соотношения сторон: 1:1, 16:9, 9:16; умное улучшение промптов
- **Vision (анализ изображений)**:
  - Ответы с учетом последних изображений, ограничение истории (настраивается)
  - Активный контекст изображения запоминается для редактирования
- **Голос**:
  - STT: распознавание голоса (Google Speech Recognition)
  - TTS: озвучка ответов через внешний API, разбиение длинных текстов на чанки
  - Голос зависит от выбранной роли
- **Подписки и лимиты**:
  - Бесплатный, Стандартный, Премиум (Telegram Stars, XTR)
  - Авто‑сброс дневных лимитов по МСК, контроль лимитов на текст/изображения
- **Рефералы и профиль**:
  - Уникальные реферальные ссылки, счетчик приглашенных
  - Выбор модели, роли, языков интерфейса (ru/en), отдельная модель для генерации изображений
- **Админ‑панель (команды)**:
  - Рассылка, статистика, сброс лимитов пользователей
- **Инструменты и MCP**:
  - Инструменты: генерация и редактирование изображений
  - Поддержка MCP‑клиентов (по конфигу), уведомления об использовании инструментов

### 🔧 Требования

- Python 3.10+
- Telegram Bot API токен
- Supabase (URL и Anon Key)
- LLM‑прокси (OpenAI‑совместимый API). По умолчанию используется `http://193.233.114.29:4000/v1`
- FFmpeg (для работы с `pydub` и аудио)

Рекомендуется установить FFmpeg:
- macOS: `brew install ffmpeg`
- Linux: `sudo apt install ffmpeg` (или пакет вашего дистрибутива)
- Windows: установить из дистрибутива FFmpeg и добавить в PATH

### 🚀 Установка и запуск

1) Клонирование и окружение

```bash
git clone https://github.com/Lorodn4x/GPT_Project.git
cd GPT_Project
python -m venv venv
```

Активация окружения:
- macOS/Linux: `source venv/bin/activate`
- Windows (PowerShell): `venv\Scripts\Activate.ps1`

2) Установка зависимостей

```bash
pip install -r requirements.txt
```

3) Настройка переменных окружения (.env)

```env
# Telegram / БД
TG_TOKEN=ваш_tg_бот_токен
SUPABASE_URL=ваш_supabase_url
SUPABASE_ANON_KEY=ваш_supabase_anon_key

# LLM (опционально, если прокси требует ключ)
LITELLM_API_KEY=ваш_llm_api_key

# Генерация изображений
OPENAI_IMAGE_KEY=api_key_для_provider-4/imagen-4
POLLINATIONS_API_TOKEN=опционально
POLLINATIONS_REFERRER=опционально

# Управление и лимиты
ADMINS=123456789,987654321   # список Telegram ID через запятую
MAX_MESSAGES=30              # хранение истории диалога
MAX_VISION_IMAGES=3          # сколько последних изображений учитывать
USER_CACHE_TTL=300           # кэш пользователей (сек)

# Голос (опционально)
TTS_VIBE_PROMPT=мягкая, дружелюбная дикция
```

4) База данных
- Создайте проект в Supabase и примените SQL‑миграции из `db/migrations/` через SQL Editor.
- При старте бот проверяет соединение и таблицы `users`/`messages`.

5) Запуск

```bash
python run.py
```

Логи пишутся в папку `logs` с ротацией по дням.

### 🎯 Команды

- `/start` — приветствие и выбор языка
- `/profile` — профиль, настройки (модель, роль, модель изображений, язык, приватность)
- `/model` — выбор модели для текста
- `/image` — генерация изображения (выбор размера → ввод промпта)
- `/new_dialog` — очистка истории диалога

- Админ: `/newsletter`, `/reset_limits <tg_id>`

### 📚 Детали реализации (кратко)

- **Модели**: см. `config/models_config.py` (GPT‑4.1 по умолчанию). Вызовы идут на OpenAI‑совместимый эндпоинт.
- **Изображения**: модели и провайдеры — `config/image_generation_config.py` (Flux/kontext — Pollinations, Imagen‑4 — OpenAI‑совместимый). Размеры: 1:1, 16:9, 9:16.
- **Инструменты**: реестр и включение — `config/tools_config.py`, реализация — `app/tools/implementations/*`.
- **Подписки**: планы и лимиты — `config/subscription_config.py` (авто‑сброс в МСК, уведомления об истечении).
- **MCP**: конфигурация — `config/mcp_servers.json` (опционально). При старте создаются сессии, есть мягкий shutdown.
- **Голос**: STT через Google Speech Recognition, TTS через внешний API; для длинных текстов — разбиение и склейка MP3.

### 💎 Тарифные планы (XTR)

- **Бесплатный**: 999 текстовых / 999 изображений в день
- **Стандартный** (99 XTR): 10000 текстовых / 10000 изображений в день
- **Премиум** (196 XTR): безлимитные текстовые и изображения

Лимиты сбрасываются ежедневно по московскому времени. При истечении платной подписки — автоматический переход на бесплатный тариф.

### 🔐 Приватность

Этот репозиторий закрытый. Описание и инструкции видны только владельцу. Не публикуйте ключи и приватные URL. Все переменные окружения храните в `.env`.
