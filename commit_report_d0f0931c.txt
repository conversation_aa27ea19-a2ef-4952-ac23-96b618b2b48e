Decoded commit message: до<PERSON><PERSON><PERSON><PERSON><PERSON>на команда /stats для администратора для просмотра статистики бота

commit d0f0931c77d9b38533e88984a969f33e8bc47193
Author: <PERSON>on4x <<EMAIL>>
Date:   Fri Jan 3 01:33:04 2025 +0300

    Added the /stats command for the administrator to view bot statistics

diff --git a/app/database/requests.py b/app/database/requests.py
index 741f9cf..066ab0b 100644
--- a/app/database/requests.py
+++ b/app/database/requests.py
@@ -4,6 +4,7 @@ import os
 import string  # Helper for string operations
 import random  # Helper for random operations and generating keys
 from config.models_config import DEFAULT_MODEL
+from datetime import datetime, timedelta
 
 async def set_user(tg_id, referral_code=None):
     async with async_session() as session:
@@ -122,3 +123,58 @@ async def set_user_settings(tg_id, settings):
             for key, value in settings.items():
                 setattr(user, key, value)
             await session.commit()
+
+async def get_stats():
+    async with async_session() as session:
+        # Get the total number of users
+        total_users = await session.scalar(select(func.count()).select_from(User))
+        
+        # Get the number of users by subscription plan
+        free_users = await session.scalar(
+            select(func.count())
+            .select_from(User)
+            .where(User.subscription_plan == "free")
+        )
+        standard_users = await session.scalar(
+            select(func.count())
+            .select_from(User)
+            .where(User.subscription_plan == "standard")
+        )
+        premium_users = await session.scalar(
+            select(func.count())
+            .select_from(User)
+            .where(User.subscription_plan == "premium")
+        )
+        
+        # Get the number of active users in the last 7 days
+        seven_days_ago = datetime.utcnow() - timedelta(days=7)
+        active_users = await session.scalar(
+            select(func.count(User.id.distinct()))
+            .select_from(User)
+            .where(User.last_request_date >= seven_days_ago)
+        )
+        
+        # Get the total number of requests
+        total_text_requests = await session.scalar(
+            select(func.sum(User.text_requests_today))
+            .select_from(User)
+        ) or 0
+        total_image_requests = await session.scalar(
+            select(func.sum(User.image_requests_today))
+            .select_from(User)
+        ) or 0
+        
+        total_requests = total_text_requests + total_image_requests
+        avg_requests = round(total_requests / total_users if total_users > 0 else 0, 2)
+        
+        return {
+            "total_users": total_users,
+            "free_users": free_users,
+            "standard_users": standard_users,
+            "premium_users": premium_users,
+            "active_users": active_users,
+            "total_requests": total_requests,
+            "avg_requests": avg_requests,
+            "total_text_requests": total_text_requests,
+            "total_image_requests": total_image_requests
+        }
diff --git a/app/handlers.py b/app/handlers.py
index d228974..d2531ee 100644
--- a/app/handlers.py
+++ b/app/handlers.py
@@ -7,7 +7,7 @@ from aiogram.fsm.state import State, StatesGroup
 from aiogram.fsm.context import FSMContext
 import app.keyboards as kb
 from app.text_messages import gpt4
-from app.database.requests import set_user, get_users, save_message, delete_user_messages, set_user_model, get_user_model, set_user_role, get_user_role, get_user_referral_count, get_user_referral_code, get_user_settings, set_user_settings
+from app.database.requests import set_user, get_users, save_message, delete_user_messages, set_user_model, get_user_model, set_user_role, get_user_role, get_user_referral_count, get_user_referral_code, get_user_settings, set_user_settings, get_stats
 from app.voice_messages import handle_voice_message
 from app.image_generation import generate_image, generate_image_with_enhancement
 from aiogram.exceptions import TelegramBadRequest
@@ -334,6 +334,28 @@ async def referral_system(message: Message):
     await message.answer(referral_info, parse_mode="Markdown")
 
 
+@router.message(AdminProtect(), Command("stats"))
+async def get_bot_stats(message: Message):
+    stats = await get_stats()
+    
+    stats_message = (
+        f"*Bot Statistics:*

"
+        f"*Users:*
"
+        f"  Total Users: `{stats['total_users']}`
"
+        f"  Active Users (7 days): `{stats['active_users']}`

"
+        f"*Plans:*
"
+        f"  Free: `{stats['free_users']}`
"
+        f"  Standard: `{stats['standard_users']}`
"
+        f"  Premium: `{stats['premium_users']}`

"
+        f"*Requests:*
"
+        f"  Total: `{stats['total_requests']}`
"
+        f"  Text Requests: `{stats['total_text_requests']}`
"
+        f"  Image Requests: `{stats['total_image_requests']}`
"
+        f"  Avg requests per user: `{stats['avg_requests']}`"
+    )
+    
+    await message.answer(stats_message, parse_mode="Markdown")
+
 @router.message(F.text)
 async def generate(message: Message, state: FSMContext, bot: Bot):
     # User is trying to send a message to the bot