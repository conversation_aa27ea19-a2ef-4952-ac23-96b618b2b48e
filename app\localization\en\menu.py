"""
Menu and navigation texts for English language
"""

TEXTS = {
    "select_option": "Select one of the following options 👇",
    "select_model": (
        "*🧠 Select a model:*\n\n"
        "🔮 *GPT-5*\n"
        "• The most powerful model from OpenAI\n"
        "• Multimodal: text, images, audio, and video\n"
        "• Highly accurate and deep reasoning\n"
        "• Automatic selection of response mode depending on the task\n"
        "• Excellent for programming and analytics\n\n"
        "⚡️ *GPT-5-nano*\n"
        "• Ultra-light and super-fast version of GPT-5\n"
        "• Instant answers to your questions\n"
        "• Perfect for quick, everyday tasks\n"
        "• High speed without sacrificing quality\n"
        "• An excellent choice for chat conversations\n\n"
        "💡 *Grok-4*\n"
        "• The most powerful model from Elon Musk and xAI\n"
        "• Multimodal: text, images, and video\n"
        "• High speed and low cost\n"
        "• Integrated reasoning and fast thinking\n"
        "• Ideal for search, programming, and analytics\n\n"
        "🧩 *Gemini 2.5 PRO*\n"
        "• The newest model from Google\n"
        "• The most advanced and powerful model in the world right now!\n"
        "• Unique reasoning and thinking capabilities\n"
        "• Knowledge up to 2025\n"
        "• Deep understanding with ability to think"
    ),
    "model_selected": "You have selected the model: {model_name}",
    "model_error": "An error occurred while selecting the model",
    "select_role": (
        "*🎭 Select a role for the bot:*\n\n"
        "👤 *Default*\n"
        "• Friendly assistant\n"
        "• Answers to the point\n"
        "• Communicates in English\n\n"
        "👩🏻‍💼 *Emily*\n"
        "• Mysterious and attractive conversationalist\n"
        "• Communication with soul and sincerity\n"
        "• Support, inspiration, and interesting conversations\n\n"
        "👨‍🔬 *Isaac*\n"
        "• Scientific approach\n"
        "• Deep analysis\n"
        "• Accurate and structured answers\n\n"
        "📚 *Tutor*\n"
        "• Help with homework\n"
        "• Detailed problem solutions\n"
        "• Works with all subjects\n\n"
        "💻 *Programmer*\n"
        "• Experienced developer with 10+ years of experience\n"
        "• Help with code in any programming languages\n"
        "• Architectural solutions and best practices\n"
        "• Debugging, optimization, and code review\n\n"
        "💪 *Fitness Trainer*\n"
        "• Professional trainer and health & wellness specialist\n"
        "• Help with workouts, nutrition, and achieving fitness goals\n"
        "• Individual programs and motivational support\n"
        "• Consultations on supplements and recovery"
    ),
    "role_selected": "You have selected the role: {display_name}",
    "models_info": "ℹ️ Select a model from the list above",
    "select_image_model": (
        "*🎨 Select an image generation model:*\n\n"
        "🚀 *Flux*\n"
        "• Revolutionary next-generation neural network\n"
        "• Incredible detail and photorealistic quality\n"
        "• Lightning-fast generation of premium images\n"
        "• Superior understanding of complex artistic styles\n"
        "• Perfect rendering of lighting, shadows, and textures\n\n"
        "🎯 *Kontext*\n"
        "• Advanced AI model for creating and editing images\n"
        "• Understands deep context and allows precise detail modification\n"
        "• Local editing of specific image parts\n"
        "• Stable work with characters, styles, and text\n"
        "• Very fast generation for interactive applications\n\n"
        "🍌 *Nano Banana*\n"
        "• Part of the Google Gemini 2.5 Flash Image ecosystem\n"
        "• Specializes in image generation and editing\n"
        "• Quickly creates and edits images while preserving details\n"
        "• Good at maintaining style and original characteristics\n"
        "• Effective for working with various types of visual content\n"
    )
}
