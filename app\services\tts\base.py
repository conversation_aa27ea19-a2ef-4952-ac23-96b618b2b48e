# app/services/tts/base.py
from typing import Protocol, Optional, Dict, Any

class TextToSpeechGenerator(Protocol):
    """
    Протокол (интерфейс) для всех провайдеров синтеза речи.
    """
    def __init__(self, config: Dict[str, Any]):
        ...

    async def generate(self, text: str, user_id: int) -> Optional[bytes]:
        """
        Основной метод для синтеза речи.

        Args:
            text: Текст для озвучивания.
            user_id: ID пользователя из БД для выбора голоса на основе роли.

        Returns:
            Байты сгенерированного аудиофайла (mp3) или None в случае ошибки.
        """
        ...
