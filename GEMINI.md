# GEMINI.md

**Примечание:** Бот должен всегда отвечать на русском языке.

## Обзор проекта

Этот проект представляет собой многофункционального Telegram-бота, созданного на Python. Он использует различные модели ИИ для генерации текста, изображений и анализа изображений. Бот также включает такие функции, как преобразование голоса в текст, преобразование текста в голос, планы подписки, реферальную систему и панель администратора.

Ядром проекта является приложение на Python, использующее библиотеку `aiogram` для взаимодействия с Telegram Bot API. Он использует бэкенд Supabase для хранения данных и LLM-прокси для доступа к различным языковым моделям.

Проект также использует **MCP (Model Context Protocol)** — открытый протокол для стандартизированного взаимодействия языковых моделей (LLM) с внешними данными и инструментами. Это обеспечивает универсальный обмен контекстом между клиентом, сервером и моделью для расширения функционала ИИ-приложений. Протокол поддерживает подключение внешних данных, вызов инструментов и управление сессиями, что упрощает интеграцию LLM в различные системы и приложения.

**Ключевые технологии:**

*   **Бэкенд:** Python 3.10+
*   **Фреймворк для Telegram-бота:** `aiogram`
*   **База данных:** Supabase
*   **Протокол для инструментов:** MCP (Model Context Protocol)
*   **Языковые модели:** GPT-4.1, GPT-4.1-nano, GPT-5-nano, Gemini 2.5 Pro (через LLM-прокси)
*   **Модели для генерации изображений:** Flux, kontext (через Pollinations), Imagen-4 (через провайдера, совместимого с OpenAI)
*   **Обработка аудио:** `pydub`, Google Speech Recognition

## Сборка и запуск

**1. Предварительные требования:**

*   Python 3.10+
*   Токен Telegram Bot API
*   Проект Supabase (URL и Anon Key)
*   LLM-прокси (API, совместимый с OpenAI)
*   FFmpeg (для обработки аудио)

**2. Установка:**

```bash
# Клонируйте репозиторий
git clone https://github.com/Lorodn4x/GPT_Project.git
cd GPT_Project

# Создайте и активируйте виртуальное окружение
python -m venv venv
# В Windows:
venv\Scripts\activate
# В macOS/Linux:
source venv/bin/activate

# Установите зависимости Python
pip install -r requirements.txt
```

**3. Конфигурация:**

*   Создайте файл `.env`, скопировав `.env.example`.
*   Заполните необходимые переменные окружения в файле `.env`, включая ваш токен Telegram, учетные данные Supabase и все необходимые API-ключи.

**4. Настройка базы данных:**

*   Создайте новый проект в Supabase.
*   Выполните SQL-скрипты из каталога `db/migrations/` в редакторе SQL Supabase, чтобы настроить схему базы данных.

**5. Запуск бота:**

```bash
python run.py
```

Бот запустится, и вы сможете взаимодействовать с ним в Telegram. Логи хранятся в каталоге `logs/`.

**6. Запуск тестов:**

Существует каталог `test`, но информация о том, как запускать тесты, не найдена.

## Соглашения о разработке

*   **Конфигурация:** Проект использует каталог `config` для хранения конфигураций моделей, подписок и инструментов. Это позволяет легко изменять поведение бота, не изменяя код.
*   **Модульность:** Приложение структурировано по модулям для обработчиков, сервисов и взаимодействий с базой данных, что способствует организации и повторному использованию кода.
*   **Динамическая загрузка:** Менеджер генерации изображений динамически загружает соответствующий класс генератора на основе конфигурации, что позволяет легко расширять его новыми провайдерами.
*   **Кэширование:** Простой кэш в памяти используется для пользовательских данных и экземпляров генератора изображений для повышения производительности.
*   **Локализация:** Бот поддерживает несколько языков (русский и английский), используя каталог `app/localization`.
*   **Обработка ошибок:** Скрипт `run.py` включает базовую обработку ошибок и ведение журнала.

## Основные задачи по разработке

*   **Добавление новых AI моделей**: 
    1.  Обновите `config/models_config.py`.
    2.  Добавьте описание модели в файлы локализации: `app/localization/ru/menu.py` и `app/localization/en/menu.py`.
*   **Добавление новых инструментов**: Создайте реализацию в `app/tools/implementations/` и зарегистрируйте в `config/tools_config.py`.
*   **Добавление новых языков**: Добавьте переводы в `app/localization/`.
*   **Изменения в базе данных**: Добавьте миграции в `db/migrations/`.