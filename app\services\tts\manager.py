# app/services/tts/manager.py
import importlib
import logging
from typing import Dict, Any

from app.services.tts.base import TextToSpeechGenerator
from config.tts_config import TTS_MODELS_CONFIG, TTS_PROVIDER_CLASSES

logger = logging.getLogger(__name__)

# Простой кеш для хранения уже созданных экземпляров генераторов
_generators_cache: Dict[str, TextToSpeechGenerator] = {}

def get_generator(model_id: str) -> TextToSpeechGenerator:
    """
    Фабричная функция. Получает инстанс TTS генератора для указанной модели.
    Использует кеширование для предотвращения повторного создания объектов.
    """
    if model_id in _generators_cache:
        return _generators_cache[model_id]

    model_config = TTS_MODELS_CONFIG.get(model_id)
    if not model_config:
        raise ValueError(f"Конфигурация для TTS модели '{model_id}' не найдена.")

    provider_name = model_config.get("provider")
    if not provider_name:
        raise ValueError(f"В конфигурации TTS модели '{model_id}' не указан провайдер.")

    provider_path = TTS_PROVIDER_CLASSES.get(provider_name)
    if not provider_path:
        raise ValueError(f"TTS провайдер '{provider_name}' не зарегистрирован в TTS_PROVIDER_CLASSES.")

    try:
        # Динамически импортируем класс провайдера по его пути
        module_path, class_name = provider_path.rsplit('.', 1)
        module = importlib.import_module(module_path)
        GeneratorClass = getattr(module, class_name)
    except (ImportError, AttributeError) as e:
        logger.exception(f"Не удалось загрузить класс TTS провайдера '{provider_path}'.")
        raise ImportError(f"Класс TTS провайдера '{provider_path}' не найден.") from e

    # Создаем экземпляр, передавая ему его личную конфигурацию
    instance = GeneratorClass(config=model_config.get("params", {}))
    _generators_cache[model_id] = instance
    logger.info(f"Создан и закеширован TTS генератор для модели '{model_id}' (провайдер: {provider_name}).")
    
    return instance
