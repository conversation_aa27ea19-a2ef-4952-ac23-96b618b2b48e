# CLAUDE.md

Этот файл предоставляет указания для Claude Code (claude.ai/code) при работе с кодом в этом репозитории.

Все общение должно вестись на русском языке. Все комментарии в коде должны быть на русском языке.

## Обзор проекта

Это приватный Telegram AI чат-бот со следующими возможностями:
- Многомодельные AI беседы (GPT-4.1, GPT-4.1-nano, GPT-5-nano, Gemini 2.5 Pro)
- Генерация и редактирование изображений
- Возможности компьютерного зрения для анализа изображений
- Обработка голосовых сообщений (STT/TTS)
- Система подписки с оплатой через Telegram Stars
- Реферальная система и профили пользователей
- Интеграция с клиентом MCP (Model Context Protocol)

## Архитектура кода

Проект следует модульной структуре:

```
├── app/                 # Основной код приложения
│   ├── handlers/        # Обработчики сообщений/команд Telegram
│   ├── tools/           # Реализации AI инструментов
│   ├── services/        # Основная бизнес-логика
│   ├── supabase/        # Слой взаимодействия с базой данных
│   ├── cache/           # Управление пользовательским кэшем
│   ├── mcp_client/      # Интеграция с клиентом MCP
│   └── localization/    # Поддержка нескольких языков
├── config/              # Файлы конфигурации
├── db/                  # Миграции базы данных
├── test/                # Файлы тестов
└── run.py              # Точка входа в приложение
```

### Ключевые компоненты

1. **Telegram бот**: Построен на aiogram 3.x, обрабатывает все взаимодействия с пользователями
2. **AI модели**: Настраиваются в `config/models_config.py`, используют прокси-эндпоинт
3. **Система инструментов**: Реализует вызов инструментов с инструментами генерации и редактирования изображений
4. **База данных**: Использует Supabase с таблицами для пользователей и сообщений
5. **Интеграция MCP**: Поддерживает клиентов протокола контекста модели для расширенных возможностей

## Команды разработки

### Настройка и установка
```bash
# Создание виртуального окружения
python -m venv venv

# Активация виртуального окружения
# Windows: venv\Scripts\Activate.ps1
# macOS/Linux: source venv/bin/activate

# Установка зависимостей
pip install -r requirements.txt
```

### Запуск приложения
```bash
# Настройка переменных окружения в файле .env
# Затем запуск бота
python run.py
```

### Ключевые переменные окружения
- `TG_TOKEN`: Токен Telegram бота
- `SUPABASE_URL`: URL проекта Supabase
- `SUPABASE_ANON_KEY`: Анонимный ключ Supabase
- `LITELLM_API_KEY`: Опциональный API ключ для прокси LLM

### Детали структуры проекта

- **Конфигурация моделей**: `config/models_config.py` определяет доступные AI модели
- **Конфигурация инструментов**: `config/tools_config.py` управляет доступными AI инструментами
- **Генерация изображений**: `config/image_generation_config.py` управляет моделями изображений
- **Подписки**: `config/subscription_config.py` определяет уровни ценообразования
- **Серверы MCP**: `config/mcp_servers.json` перечисляет доступные серверы MCP

### Основная точка входа
Приложение запускается в `run.py`, который инициализирует:
1. Систему логирования с ротацией файлов
2. Подключение к базе данных Supabase
3. Telegram бота с обработчиками
4. Менеджер клиентов MCP
5. Систему пользовательского кэша

### Система обработчиков
Обработчики в `app/handlers/` управляют различными функциями бота:
- `base.py`: Команда запуска и выбор языка
- `profile.py`: Управление профилем пользователя
- `text_processing.py`: Основная функциональность AI чата
- `image_generation.py`: Команды создания изображений
- `vision.py`: Возможности анализа изображений
- `voice.py`: Обработка голосовых сообщений
- `admin.py`: Административные команды

### Реализация инструментов
AI инструменты реализованы в `app/tools/implementations/`:
- `image_generator_tool.py`: Создает изображения из текстовых запросов
- `edit_image_tool.py`: Изменяет существующие изображения на основе описаний

Каждый инструмент реализует протокол Tool с именем, описанием, схемой и методом выполнения.

### Слой базы данных
Интеграция Supabase в `app/supabase/` обрабатывает:
- Управление пользователями и настройками
- Хранение истории сообщений
- Отслеживание подписок
- Данные реферальной системы

## Общие задачи разработки

1. **Добавление новых AI моделей**:
   - Обновить `config/models_config.py`.
   - Добавить описание модели в файлы локализации: `app/localization/ru/menu.py` и `app/localization/en/menu.py`.
2. **Добавление новых инструментов**: Создать реализацию в `app/tools/implementations/` и зарегистрировать в `config/tools_config.py`
3. **Добавление новых языков**: Добавить переводы в `app/localization/`
4. **Изменения в базе данных**: Добавить миграции в `db/migrations/`