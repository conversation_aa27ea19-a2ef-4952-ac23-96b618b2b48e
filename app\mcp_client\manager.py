import asyncio
import json
import logging
import os
import signal
from typing import Dict, Optional, List, Any
from contextlib import AsyncExitStack

# Исправленные импорты
from mcp import client as mcp_client, ClientSession, types as mcp_types
from mcp.client.stdio import stdio_client, StdioServerParameters

logger = logging.getLogger(__name__)

CONFIG_PATH = os.path.join("config", "mcp_servers.json")

class MCPClientManager:
    """
    Управляет запуском MCP серверов как подпроцессов,
    подключением к ним и жизненным циклом сессий.
    Использует mcp-sdk для запуска процессов и AsyncExitStack для управления ресурсами.
    """

    def __init__(self, config_path: str = CONFIG_PATH):
        self._config_path = config_path
        self._server_configs: Dict[str, Dict[str, Any]] = {}
        self._sessions: Dict[str, ClientSession] = {}
        self._exit_stack = AsyncExitStack()
        self._load_config()

    def _load_config(self):
        """Загружает конфигурацию MCP серверов из JSON файла."""
        try:
            if os.path.exists(self._config_path):
                with open(self._config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self._server_configs = config.get("mcpServers", {})
                    if not self._server_configs:
                        logger.warning(f"Файл конфигурации MCP '{self._config_path}' пуст или не содержит ключ 'mcpServers'. MCP клиенты не будут запущены.")
                    else:
                         logger.info(f"Загружена конфигурация для {len(self._server_configs)} MCP серверов из '{self._config_path}'.")
            else:
                logger.warning(f"Файл конфигурации MCP '{self._config_path}' не найден. MCP клиенты не будут запущены.")
        except json.JSONDecodeError:
            logger.exception(f"Ошибка декодирования JSON в файле конфигурации MCP '{self._config_path}'.")
        except Exception:
            logger.exception(f"Не удалось загрузить конфигурацию MCP из '{self._config_path}'.")

    async def startup(self):
        """Запускает настроенные MCP серверы и устанавливает соединения."""
        enabled_servers = {name: config for name, config in self._server_configs.items() if not config.get("disabled", False)}

        if not enabled_servers:
            logger.info("Нет активных MCP серверов для запуска.")
            return

        logger.info(f"Запуск {len(enabled_servers)} активных MCP серверов...")
        start_tasks = []
        for server_name, config in enabled_servers.items():
            start_tasks.append(self._start_server(server_name, config))

        results = await asyncio.gather(*start_tasks, return_exceptions=True)

        successful_starts = 0
        for i, result in enumerate(results):
            server_name = list(enabled_servers.keys())[i]
            if not isinstance(result, Exception) and result:
                successful_starts += 1
            elif isinstance(result, Exception):
                 logger.error(f"Не удалось запустить или подключиться к MCP серверу '{server_name}': {result}")

        if successful_starts > 0:
             logger.info(f"Успешно запущено и подключено {successful_starts} из {len(enabled_servers)} MCP серверов.")
        elif len(enabled_servers) > 0:
             logger.warning(f"Не удалось запустить ни один из {len(enabled_servers)} активных MCP серверов.")


    async def _start_server(self, server_name: str, config: Dict[str, Any]) -> bool:
        """Запускает один MCP сервер с помощью mcp-sdk и устанавливает сессию."""
        command = config.get("command")
        args = config.get("args", [])
        env = config.get("env", {})

        if not command:
            logger.error(f"Не указана команда для запуска сервера '{server_name}'.")
            return False

        # Подставляем значения переменных окружения из системы
        for key, value in env.items():
            env_value = os.environ.get(key)
            if env_value:
                logger.info(f"Используем переменную окружения {key} для сервера '{server_name}'")
                env[key] = env_value
            else:
                logger.warning(f"Переменная окружения {key} не найдена для сервера '{server_name}', используем значение из конфигурации")

        server_params = StdioServerParameters(
            command=command,
            args=args,
            env=env if env else None
        )
        self._server_configs[server_name]['_params'] = server_params

        try:
            logger.info(f"Попытка запуска и подключения к MCP серверу '{server_name}' через mcp-sdk...")

            # Используем exit_stack для управления ресурсами
            stdio_transport = await self._exit_stack.enter_async_context(stdio_client(server_params))
            stdin_stream, stdout_stream = stdio_transport

            # Создаем и регистрируем сессию в exit_stack
            session = await self._exit_stack.enter_async_context(ClientSession(stdin_stream, stdout_stream))
            self._sessions[server_name] = session # Сохраняем сессию

            logger.info(f"Попытка инициализации MCP сессии с сервером '{server_name}'...")
            # Вызываем initialize без явных опций
            init_result = await session.initialize()

            # --- ИЗМЕНЕНИЕ ЗДЕСЬ ---
            # Проверяем, что результат инициализации не None
            if init_result:
                # Пытаемся получить server_info, но не делаем это условием успеха
                server_info = getattr(init_result, 'server_info', None)
                server_name_log = getattr(server_info, 'name', 'N/A')
                server_version_log = getattr(server_info, 'version', 'N/A')
                server_caps_log = getattr(init_result, 'capabilities', 'N/A')

                logger.info(f"MCP сессия с сервером '{server_name}' успешно инициализирована. "
                            f"Info: {server_name_log} v{server_version_log}. "
                            f"Capabilities: {server_caps_log}")
                return True
            else:
                # Если init_result равен None (хотя обычно должно быть исключение)
                logger.error(f"Ошибка инициализации сессии с сервером '{server_name}': initialize() вернул None.")
                if server_name in self._sessions: del self._sessions[server_name]
                return False
            # -----------------------

        except FileNotFoundError:
             logger.error(f"Команда '{command}' для запуска сервера '{server_name}' не найдена.")
             if server_name in self._sessions: del self._sessions[server_name]
             return False
        except Exception as e:
            # Логируем полное исключение для диагностики
            logger.exception(f"Ошибка при запуске или подключении к MCP серверу '{server_name}': {e}")
            if server_name in self._sessions: del self._sessions[server_name]
            return False

    async def shutdown(self):
        """Закрывает все MCP сессии и ресурсы, управляемые AsyncExitStack."""
        logger.info("Остановка MCP клиента и закрытие ресурсов...")
        try:
            await self._exit_stack.aclose()
            logger.info(f"Все MCP сессии ({len(self._sessions)}) и связанные ресурсы успешно закрыты.")
        except Exception as e:
            logger.exception(f"Ошибка при закрытии AsyncExitStack: {e}")
        finally:
            self._sessions.clear()
            logger.info("MCP клиент остановлен.")

    def get_active_sessions(self) -> Dict[str, ClientSession]:
        """Возвращает словарь активных MCP сессий."""
        return self._sessions.copy()